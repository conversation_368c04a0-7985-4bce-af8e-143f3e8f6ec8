"""
认证相关API端点

提供用户登录、微信登录、token刷新等认证功能
"""
from datetime import datetime, timedelta
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user, get_current_user_id, TokenBlacklist
from app.services.user_service import UserService
from app.schemas.user import UserLogin, UserResponse
from app.schemas.common import TokenResponse, ResponseModel
from app.utils.security import create_access_token, verify_token
from app.utils.wechat import wechat_api, WeChatAPIError, WeChatUserInfo
from app.config import settings

router = APIRouter()
security = HTTPBearer()
user_service = UserService()


@router.post("/login", response_model=ResponseModel[TokenResponse], summary="用户登录")
async def login(
    user_credentials: UserLogin,
    db: Session = Depends(get_db)
) -> ResponseModel[TokenResponse]:
    """
    用户登录接口
    
    - **username**: 用户名
    - **password**: 密码
    
    返回JWT访问令牌和用户信息
    """
    try:
        # 用户认证
        user = user_service.authenticate(
            db, user_credentials.username, user_credentials.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账户已被禁用"
            )
        
        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id), "username": user.username},
            expires_delta=access_token_expires
        )
        
        token_data = TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user=UserResponse.from_orm(user)
        )
        
        return ResponseModel(
            code=200,
            message="登录成功",
            data=token_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/wechat-miniprogram-login", response_model=ResponseModel[TokenResponse], summary="微信小程序登录")
async def wechat_miniprogram_login(
    login_data: Dict[str, Any],
    db: Session = Depends(get_db)
) -> ResponseModel[TokenResponse]:
    """
    微信小程序登录接口

    - **code**: 小程序调用wx.login()获得的临时登录凭证code

    返回JWT访问令牌和用户信息
    """
    try:
        code = login_data.get("code")
        if not code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少小程序登录凭证code"
            )

        # 通过微信小程序API获取用户信息
        try:
            session_data = await wechat_api.miniprogram_login(code)
            normalized_info = WeChatUserInfo.normalize_miniprogram_info(session_data)
            user_profile = WeChatUserInfo.extract_user_profile(normalized_info)

        except WeChatAPIError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"小程序登录失败: {e.message}"
            )

        # 微信登录或注册
        openid = user_profile.get("wechat_openid")
        if not openid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法获取微信用户标识"
            )

        user = user_service.wechat_login(db, openid, user_profile)

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账户已被禁用"
            )

        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id), "username": user.username or user.wechat_openid},
            expires_delta=access_token_expires
        )

        token_data = TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user=UserResponse.from_orm(user)
        )

        return ResponseModel(
            code=200,
            message="小程序登录成功",
            data=token_data
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/wechat-login", response_model=ResponseModel[TokenResponse], summary="微信登录（自动识别小程序/公众号）")
async def wechat_login(
    wechat_data: Dict[str, Any],
    db: Session = Depends(get_db)
) -> ResponseModel[TokenResponse]:
    """
    微信登录接口（自动识别小程序jscode或公众号OAuth2 code）

    - **code**: 微信授权码或小程序jscode

    返回JWT访问令牌和用户信息
    """
    try:
        code = wechat_data.get("code")
        if not code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少微信授权码"
            )

        # 通过微信API获取用户信息
        try:
            # 首先尝试小程序登录方式
            try:
                session_data = await wechat_api.miniprogram_login(code)
                normalized_info = WeChatUserInfo.normalize_miniprogram_info(session_data)
                user_profile = WeChatUserInfo.extract_user_profile(normalized_info)
                login_type = "小程序"

            except WeChatAPIError as e:
                # 如果小程序登录失败，尝试OAuth2方式
                if "invalid oauth code" in str(e.message) and "miniprogram jscode" in str(e.message):
                    # 这个错误说明确实是小程序jscode，但API调用失败了
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"小程序登录失败: {e.message}"
                    )
                else:
                    # 尝试OAuth2方式
                    wechat_user_data = await wechat_api.login_with_code(code)
                    normalized_info = WeChatUserInfo.normalize_user_info(wechat_user_data)
                    user_profile = WeChatUserInfo.extract_user_profile(normalized_info)
                    login_type = "公众号"

        except WeChatAPIError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"微信登录失败: {e.message}"
            )

        # 微信登录或注册
        openid = user_profile.get("wechat_openid")
        if not openid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法获取微信用户标识"
            )

        user = user_service.wechat_login(db, openid, user_profile)

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账户已被禁用"
            )

        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id), "username": user.username or user.wechat_openid},
            expires_delta=access_token_expires
        )

        token_data = TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user=UserResponse.from_orm(user)
        )

        return ResponseModel(
            code=200,
            message=f"微信{login_type}登录成功",
            data=token_data
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"微信登录失败: {str(e)}"
        )


@router.post("/refresh", response_model=ResponseModel[TokenResponse], summary="刷新令牌")
async def refresh_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> ResponseModel[TokenResponse]:
    """
    刷新访问令牌
    
    需要在请求头中提供有效的Bearer token
    """
    try:
        # 验证当前token
        payload = verify_token(credentials.credentials)
        user_id = int(payload.get("sub"))
        
        # 获取用户信息
        user = user_service.get(db, user_id)
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 生成新的访问令牌
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id), "username": user.username or user.wechat_openid},
            expires_delta=access_token_expires
        )
        
        token_data = TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user=UserResponse.from_orm(user)
        )
        
        return ResponseModel(
            code=200,
            message="令牌刷新成功",
            data=token_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌刷新失败"
        )


@router.get("/me", response_model=ResponseModel[UserResponse], summary="获取当前用户信息")
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> ResponseModel[UserResponse]:
    """
    获取当前登录用户的详细信息
    
    需要在请求头中提供有效的Bearer token
    """
    try:
        # 验证token并获取用户ID
        payload = verify_token(credentials.credentials)
        user_id = int(payload.get("sub"))
        
        # 获取用户信息
        user = user_service.get(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return ResponseModel(
            code=200,
            message="获取用户信息成功",
            data=UserResponse.from_orm(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="获取用户信息失败"
        )


@router.post("/logout", response_model=ResponseModel[None], summary="用户登出")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> ResponseModel[None]:
    """
    用户登出接口

    将当前token加入黑名单，使其失效
    """
    try:
        # 将token加入黑名单
        token = credentials.credentials
        TokenBlacklist.add_token(token)

        return ResponseModel(
            code=200,
            message="登出成功",
            data=None
        )

    except Exception as e:
        # 即使加入黑名单失败，也返回成功（客户端删除token即可）
        return ResponseModel(
            code=200,
            message="登出成功",
            data=None
        )


@router.get("/wechat-oauth-url", response_model=ResponseModel[Dict[str, str]], summary="获取微信OAuth授权URL")
async def get_wechat_oauth_url(
    redirect_uri: str,
    state: str = ""
) -> ResponseModel[Dict[str, str]]:
    """
    获取微信OAuth授权URL

    - **redirect_uri**: 回调地址
    - **state**: 状态参数（可选）

    返回微信授权URL
    """
    try:
        oauth_url = wechat_api.get_oauth_url(redirect_uri, state=state)

        return ResponseModel(
            code=200,
            message="获取微信授权URL成功",
            data={
                "oauth_url": oauth_url,
                "app_id": settings.wechat_app_id,
                "redirect_uri": redirect_uri,
                "state": state
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取微信授权URL失败: {str(e)}"
        )


@router.post("/wechat-callback", response_model=ResponseModel[TokenResponse], summary="微信登录回调")
async def wechat_callback(
    callback_data: Dict[str, Any],
    db: Session = Depends(get_db)
) -> ResponseModel[TokenResponse]:
    """
    微信登录回调处理

    - **code**: 微信授权码
    - **state**: 状态参数

    处理微信回调并完成登录
    """
    try:
        code = callback_data.get("code")
        state = callback_data.get("state", "")

        if not code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少微信授权码"
            )

        # 检查是否有错误
        error = callback_data.get("error")
        if error:
            error_description = callback_data.get("error_description", "微信授权失败")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"微信授权失败: {error_description}"
            )

        # 使用微信登录接口
        return await wechat_login({"code": code}, db)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"微信回调处理失败: {str(e)}"
        )
