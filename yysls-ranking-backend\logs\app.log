2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [generated in 0.01104s] {'id_1': 1}
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - [cached since 0.3025s ago] {'id_1': 1}
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - [cached since 22.01s ago] {'id_1': 1}
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - [generated in 0.00105s] {}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - [generated in 0.00139s] {'param_1': 0, 'param_2': 2}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - [cached since 2.741s ago] {}
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - [cached since 2.765s ago] {'param_1': 0, 'param_2': 2}
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - [generated in 0.00083s] {'id_1': 1}
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 11:56:26 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [generated in 0.00179s] {'username_1': 'admin'}
2025-08-01 11:57:29 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [generated in 0.00082s] {'last_login_at': datetime.datetime(2025, 8, 1, 3, 57, 29, 922243), 'users_id': 1}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 11:57:30 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00158s] {'pk_1': 1}
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 11:57:50 - app.api.v1.endpoints.upload - INFO - 用户1临时上传Excel文件: 榜单明细模板.xlsx -> a80ca6e93c7947f0a6f361bc43c5bcdf_1.xlsx
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00143s] {'id_1': 11}
2025-08-01 12:01:38 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - [cached since 2.949s ago] {'id_1': 1}
2025-08-01 12:01:41 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - [cached since 44.02s ago] {'id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [cached since 44.19s ago] {'id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [generated in 0.00083s] {'pk_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [generated in 0.00133s] {'ranking_id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:02:22 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: (pymysql.err.OperationalError) (1054, "Unknown column 'ranking_details.rank_start' in 'field list'")
[SQL: SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s]
[parameters: {'ranking_id_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00161s] {'id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [cached since 0.09514s ago] {'id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00117s] {'pk_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00096s] {'ranking_id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:11:40 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: (pymysql.err.OperationalError) (1054, "Unknown column 'ranking_details.rank_start' in 'field list'")
[SQL: SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s]
[parameters: {'ranking_id_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:29:44 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - [generated in 0.00124s] {'username_1': 'admin'}
2025-08-01 12:30:36 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00091s] {'last_login_at': datetime.datetime(2025, 8, 1, 4, 30, 37, 39032), 'users_id': 1}
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:30:37 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00189s] {'pk_1': 1}
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:31:08 - app.api.v1.endpoints.upload - INFO - 用户1临时上传Excel文件: 榜单明细模板.xlsx -> a6e2cf0c43474e8882f8e4d73cb1a4b3_1.xlsx
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00111s] {'id_1': 1}
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.1053s ago] {'id_1': 1}
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00089s] {'pk_1': 1}
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00189s] {'ranking_id_1': 1}
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:31:26 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: 'ChunkedIteratorResult' object has no attribute 'delete'
2025-08-01 12:33:23 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:33:23 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00153s] {'id_1': 1}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.08431s ago] {'id_1': 1}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00091s] {'pk_1': 1}
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - [generated in 0.00079s] {'ranking_id_1': 1}
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:33:25 - app.utils.excel_handler - INFO - 成功解析Excel文件，共5条记录
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - [generated in 0.00087s] {'ranking_id': 1, 'rank_start': 1, 'rank_end': 5, 'completion_time': datetime.time(0, 5, 30), 'completion_seconds': 330, 'participant_count': 5, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '燕友圈战队', 'created_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802), 'updated_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802)}
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:33:25 - RankingService - ERROR - 导入Excel榜单明细失败 ranking_id=1: (pymysql.err.OperationalError) (1364, "Field 'participant_name' doesn't have a default value")
[SQL: INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)]
[parameters: {'ranking_id': 1, 'rank_start': 1, 'rank_end': 5, 'completion_time': datetime.time(0, 5, 30), 'completion_seconds': 330, 'participant_count': 5, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '燕友圈战队', 'created_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802), 'updated_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:33:25 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: (pymysql.err.OperationalError) (1364, "Field 'participant_name' doesn't have a default value")
[SQL: INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)]
[parameters: {'ranking_id': 1, 'rank_start': 1, 'rank_end': 5, 'completion_time': datetime.time(0, 5, 30), 'completion_seconds': 330, 'participant_count': 5, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '燕友圈战队', 'created_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802), 'updated_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - [generated in 0.00147s] {'id_1': 1}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - [cached since 0.08203s ago] {'id_1': 1}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - [generated in 0.00111s] {'pk_1': 1}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - [generated in 0.00103s] {'ranking_id_1': 1}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:34:24 - app.utils.excel_handler - INFO - 成功解析Excel文件，共5条记录
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00114s] {'ranking_id': 1, 'rank_start': 1, 'rank_end': 5, 'completion_time': datetime.time(0, 5, 30), 'completion_seconds': 330, 'participant_count': 5, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '燕友圈战队', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 150290), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 150290)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.0963s ago] {'ranking_id': 1, 'rank_start': 6, 'rank_end': 10, 'completion_time': datetime.time(0, 6, 15), 'completion_seconds': 375, 'participant_count': 5, 'team_info': '队伍F,队伍G,队伍H,队伍I,队伍J', 'team_name': '竞速联盟', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 245291), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 245291)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.197s ago] {'ranking_id': 1, 'rank_start': 11, 'rank_end': 15, 'completion_time': datetime.time(0, 7), 'completion_seconds': 420, 'participant_count': 5, 'team_info': '队伍K,队伍L,队伍M,队伍N,队伍O', 'team_name': '速度之星', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 346201), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 346201)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.3001s ago] {'ranking_id': 1, 'rank_start': 16, 'rank_end': 20, 'completion_time': datetime.time(0, 7, 45), 'completion_seconds': 465, 'participant_count': 5, 'team_info': '队伍P,队伍Q,队伍R,队伍S,队伍T', 'team_name': '闪电小队', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 449263), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 449263)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.4016s ago] {'ranking_id': 1, 'rank_start': 21, 'rank_end': 25, 'completion_time': datetime.time(0, 8, 30), 'completion_seconds': 510, 'participant_count': 5, 'team_info': '队伍U,队伍V,队伍W,队伍X,队伍Y', 'team_name': '极速团队', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 551044), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 551044)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:34:26 - RankingService - INFO - 成功导入5条榜单明细数据到榜单1
2025-08-01 12:34:26 - RankingService - INFO - 清理临时Excel文件: uploads\temp\excel\a6e2cf0c43474e8882f8e4d73cb1a4b3_1.xlsx
2025-08-01 12:34:27 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:34:27 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id AS rankings_id, rankings.name AS rankings_name, rankings.period AS rankings_period, rankings.ranking_type AS rankings_ranking_type, rankings.start_time AS rankings_start_time, rankings.end_time AS rankings_end_time, rankings.team_size_limit AS rankings_team_size_limit, rankings.total_participants AS rankings_total_participants, rankings.status AS rankings_status, rankings.created_at AS rankings_created_at, rankings.updated_at AS rankings_updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:34:27 - sqlalchemy.engine.Engine - INFO - [generated in 0.00175s] {'pk_1': 1}
2025-08-01 12:34:27 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - [generated in 0.00085s] {}
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - [generated in 0.00161s] {'param_1': 0, 'param_2': 2}
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - [cached since 40.13s ago] {}
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - [cached since 40.14s ago] {'param_1': 0, 'param_2': 2}
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - [generated in 0.00135s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - [generated in 0.00096s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - [cached since 24.27s ago] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - [cached since 24.27s ago] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00124s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00112s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:04:39 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [generated in 0.00151s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [generated in 0.00279s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:05:33 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [generated in 0.00090s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [generated in 0.00149s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:05:42 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [generated in 0.00110s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [generated in 0.00139s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00119s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00151s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00115s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00131s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 15:18:17 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00167s] {'username_1': 'admin'}
2025-08-01 15:18:24 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00097s] {'last_login_at': datetime.datetime(2025, 8, 1, 7, 18, 24, 860056), 'users_id': 1}
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:18:25 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - [generated in 0.00147s] {'pk_1': 1}
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - [cached since 89.58s ago] {'username_1': 'admin'}
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - [cached since 89.57s ago] {'last_login_at': datetime.datetime(2025, 8, 1, 7, 19, 54, 431015), 'users_id': 1}
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:19:54 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - [cached since 89.5s ago] {'pk_1': 1}
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 15:20:25 - app.api.v1.endpoints.upload - INFO - 用户1临时上传Excel文件: 榜单明细模板.xlsx -> c2e5a091572d4c9fb4e4afc7ba7e3a65_1.xlsx
2025-08-01 15:20:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:20:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 15:20:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00089s] {'id_1': 2}
2025-08-01 15:20:40 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - [cached since 22.08s ago] {'id_1': 2}
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - [cached since 22.19s ago] {'id_1': 2}
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00107s] {'pk_1': 2}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00069s] {'ranking_id_1': 2}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:21:03 - app.utils.excel_handler - INFO - 成功解析Excel文件，共5条记录
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00093s] {'ranking_id': 2, 'rank_start': 1, 'rank_end': 1, 'completion_time': datetime.time(1, 30), 'completion_seconds': 1800, 'participant_count': 1, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 552788), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 552788)}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.1021s ago] {'ranking_id': 2, 'rank_start': 6, 'rank_end': 10, 'completion_time': datetime.time(0, 6, 15), 'completion_seconds': 375, 'participant_count': 5, 'team_info': '队伍F,队伍G,队伍H,队伍I,队伍J', 'team_name': '竞速联盟', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 654815), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 654815)}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.2037s ago] {'ranking_id': 2, 'rank_start': 11, 'rank_end': 15, 'completion_time': datetime.time(0, 7), 'completion_seconds': 420, 'participant_count': 5, 'team_info': '队伍K,队伍L,队伍M,队伍N,队伍O', 'team_name': '速度之星', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 755805), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 755805)}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.307s ago] {'ranking_id': 2, 'rank_start': 16, 'rank_end': 20, 'completion_time': datetime.time(0, 7, 45), 'completion_seconds': 465, 'participant_count': 5, 'team_info': '队伍P,队伍Q,队伍R,队伍S,队伍T', 'team_name': '闪电小队', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 858754), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 858754)}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.409s ago] {'ranking_id': 2, 'rank_start': 21, 'rank_end': 25, 'completion_time': datetime.time(0, 8, 30), 'completion_seconds': 510, 'participant_count': 5, 'team_info': '队伍U,队伍V,队伍W,队伍X,队伍Y', 'team_name': '极速团队', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 961164), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 961164)}
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:21:04 - RankingService - INFO - 成功导入5条榜单明细数据到榜单2
2025-08-01 15:21:04 - RankingService - INFO - 清理临时Excel文件: uploads\temp\excel\c2e5a091572d4c9fb4e4afc7ba7e3a65_1.xlsx
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id AS rankings_id, rankings.name AS rankings_name, rankings.period AS rankings_period, rankings.ranking_type AS rankings_ranking_type, rankings.start_time AS rankings_start_time, rankings.end_time AS rankings_end_time, rankings.team_size_limit AS rankings_team_size_limit, rankings.total_participants AS rankings_total_participants, rankings.status AS rankings_status, rankings.created_at AS rankings_created_at, rankings.updated_at AS rankings_updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - [generated in 0.00087s] {'pk_1': 2}
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 16:13:28 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/oauth2/access_token?appid=your-wechat-app-id&secret=your-wechat-app-secret&code=0e1RCdll2eDT2g4TTJkl2cwq5Y2RCdlp&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:13:28 - app.utils.wechat - ERROR - 微信获取访问令牌失败: 40013 - invalid appid, rid: 688c7728-54e942e9-07bc5c92
2025-08-01 16:13:28 - app.utils.wechat - ERROR - 获取微信访问令牌异常: 微信API错误: invalid appid, rid: 688c7728-54e942e9-07bc5c92
2025-08-01 16:16:09 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/oauth2/access_token?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&code=0e1RCdll2eDT2g4TTJkl2cwq5Y2RCdlp&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:16:09 - app.utils.wechat - ERROR - 微信获取访问令牌失败: 40242 - invalid oauth code, it is miniprogram jscode, please use jscode2session, rid: 688c77c9-6e44118c-345043d2
2025-08-01 16:16:09 - app.utils.wechat - ERROR - 获取微信访问令牌异常: 微信API错误: invalid oauth code, it is miniprogram jscode, please use jscode2session, rid: 688c77c9-6e44118c-345043d2
2025-08-01 16:29:21 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/oauth2/access_token?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&code=0e1RCdll2eDT2g4TTJkl2cwq5Y2RCdlp&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:29:21 - app.utils.wechat - ERROR - 微信获取访问令牌失败: 40163 - code been used, rid: 688c7ae0-294fc0f1-22f5adf5
2025-08-01 16:29:21 - app.utils.wechat - ERROR - 获取微信访问令牌异常: 微信API错误: code been used, rid: 688c7ae0-294fc0f1-22f5adf5
2025-08-01 16:30:26 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/oauth2/access_token?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&code=0a1O2wll2BF83g4rmJkl2hnnOf2O2wlE&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:30:26 - app.utils.wechat - ERROR - 微信获取访问令牌失败: 40242 - invalid oauth code, it is miniprogram jscode, please use jscode2session, rid: 688c7b22-0b2a61a5-000f2662
2025-08-01 16:30:26 - app.utils.wechat - ERROR - 获取微信访问令牌异常: 微信API错误: invalid oauth code, it is miniprogram jscode, please use jscode2session, rid: 688c7b22-0b2a61a5-000f2662
2025-08-01 16:37:15 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0a1O2wll2BF83g4rmJkl2hnnOf2O2wlE&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:37:15 - app.utils.wechat - ERROR - 微信小程序登录失败: 40163 - code been used, rid: 688c7cba-7ddb3bef-5a731392
2025-08-01 16:37:15 - app.utils.wechat - ERROR - 小程序登录异常: 微信API错误: code been used, rid: 688c7cba-7ddb3bef-5a731392
